[project]
name = "cpp-inheritance-analyzer"
version = "0.1.0"
description = "C++ Class Inheritance Analyzer - A high-performance tool for analyzing C++ class inheritance relationships"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
dependencies = [
    "graphviz>=0.20.1",
    "matplotlib>=3.7.0",
    "clang>=16.0.0",
    "libclang>=16.0.0",
    "watchdog>=3.0.0",
    "flask>=2.0.0",
    "flask-cors>=3.0.0",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Documentation",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "ruff>=0.1.0",
]

[project.scripts]
cpp-inheritance-analyzer = "cpp_inheritance_analyzer:main"

[project.urls]
"Homepage" = "https://github.com/joelongs/cpp-inheritance-analyzer"
"Bug Tracker" = "https://github.com/joelongs/cpp-inheritance-analyzer/issues"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"



[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I", "N", "B", "W", "C4", "UP", "ANN", "RUF"]
ignore = ["B010", "B019", "E501", "ANN001", "ANN201", "ANN204", "RUF013", "F401"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
