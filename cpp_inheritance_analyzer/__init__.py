"""
C++ Class Inheritance Analyzer
A high-performance, modular tool for analyzing class inheritance relationships
from C++ codebases and generating visual diagrams.
"""

import argparse
import logging
import signal
import sys

from cpp_inheritance_analyzer.analyzer import CppInheritanceAnalyzer

__version__ = "0.1.0"

# Global variable to track interrupt status
interrupted = False

# Try to import file watcher
try:
    from cpp_inheritance_analyzer.utils.file_watcher import main as watcher_main

    HAS_WATCHER = True
except ImportError:
    HAS_WATCHER = False


def signal_handler(sig: int, frame: object) -> None:
    """Handle Ctrl+C interrupt signal"""
    global interrupted
    if not interrupted:
        print("\n\nInterrupt request received, gracefully stopping analysis...\n")
        print("Saving processed data, please wait...")
        interrupted = True
    else:
        print("\nPress Ctrl+C again to force exit (data may be lost)")
        # If user presses Ctrl+C again, restore default handler to allow forced exit
        signal.signal(signal.SIGINT, signal.SIG_DFL)


def analyze_command(args) -> int:
    """Run the analyzer with the given arguments."""
    log_level = logging.DEBUG if args.verbose else logging.INFO
    analyzer = CppInheritanceAnalyzer(
        log_level,
        parser_type=args.parser,
        use_cache=not args.no_cache,
        cache_dir=args.cache_dir,
    )

    try:
        # Analyze codebase
        analyzer.analyze_codebase(args.directory, set(args.extensions))

        # If interrupted during codebase analysis, exit early
        global interrupted
        if interrupted:
            print("Analysis interrupted, but processed data has been saved to cache.")
            return 130  # Standard SIGINT exit code

        # Analyze specific class
        family_graph = analyzer.analyze_class(args.class_name, args.output, args.format)

        if family_graph:
            print(f"Analysis complete! Results saved to {args.output}")
            print(f"Found {len(family_graph.classes)} related classes")
        else:
            print("Analysis failed - class not found")
            return 1

    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        return 130
    except Exception as e:
        logging.error(f"Analysis failed: {e}")
        return 1

    return 0


def index_command(args) -> int:
    """Index an entire codebase and generate cache for all classes."""
    log_level = logging.DEBUG if args.verbose else logging.INFO
    analyzer = CppInheritanceAnalyzer(
        log_level,
        parser_type=args.parser,
        use_cache=True,  # Always use cache for indexing
        cache_dir=args.cache_dir,
    )

    try:
        print(f"Indexing codebase in: {args.directory}")
        print("This will parse all files and generate cache for future searches.")

        # Analyze codebase without specifying a target class
        graph = analyzer.analyze_codebase(args.directory, set(args.extensions))

        # If interrupted during indexing, exit early
        global interrupted
        if interrupted:
            print("Indexing interrupted, but processed data has been saved to cache.")
            return 130  # Standard SIGINT exit code

        print(f"Indexing complete! Cached {len(graph.classes)} classes")
        print(f"Cache location: {analyzer.parser.cache_dir}")

    except KeyboardInterrupt:
        print("\nIndexing interrupted by user")
        return 130
    except Exception as e:
        logging.error(f"Indexing failed: {e}")
        return 1

    return 0


def search_command(args) -> int:
    """Search for a class using the pre-built index."""
    log_level = logging.DEBUG if args.verbose else logging.INFO
    analyzer = CppInheritanceAnalyzer(
        log_level,
        parser_type=args.parser,
        use_cache=True,  # Always use cache for searching
        cache_dir=args.cache_dir,
    )

    try:
        # Load from cache without parsing files
        print(f"Searching for class: {args.class_name}")
        family_graph = analyzer.search_class(args.class_name, args.output, args.format)

        if family_graph:
            print(f"Search complete! Results saved to {args.output}")
            print(f"Found {len(family_graph.classes)} related classes")
        else:
            print("Search failed - class not found in cache")
            print("Try running 'index' command first to build the cache")
            return 1

    except KeyboardInterrupt:
        print("\nSearch interrupted by user")
        return 130
    except Exception as e:
        logging.error(f"Search failed: {e}")
        return 1

    return 0


def main() -> int:
    """Command-line entry point for the analyzer."""
    # Set up signal handler to catch Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)

    # Create main parser
    parser = argparse.ArgumentParser(description="C++ Class Inheritance Analyzer")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Common arguments for multiple commands
    def add_common_args(cmd_parser):
        """Add common arguments to a command parser."""
        cmd_parser.add_argument(
            "-v", "--verbose", action="store_true", help="Enable verbose logging"
        )
        cmd_parser.add_argument(
            "--extensions",
            nargs="+",
            default=[".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx"],
            help="File extensions to parse",
        )
        cmd_parser.add_argument(
            "--parser",
            default="auto",
            choices=["regex", "clang", "auto"],
            help="Parser to use (regex: faster but less accurate, clang: slower but more accurate, "
            "auto: use clang if available)",
        )
        cmd_parser.add_argument(
            "--cache-dir",
            help="Directory to store cache files (default: ~/.cache/cpp_inheritance_analyzer)",
        )

    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze C++ inheritance")
    analyze_parser.add_argument("directory", help="Directory to analyze")
    analyze_parser.add_argument("class_name", help="Target class name to analyze")
    analyze_parser.add_argument(
        "-o", "--output", default="/tmp", help="Output directory for results"
    )
    analyze_parser.add_argument(
        "-f",
        "--format",
        default="png",
        choices=["png", "svg", "pdf"],
        help="Image output format",
    )
    add_common_args(analyze_parser)

    # Cache control options for analyze command
    cache_group = analyze_parser.add_argument_group("Cache Options")
    cache_group.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable persistent caching (always reanalyze files)",
    )

    # Index command
    index_parser = subparsers.add_parser(
        "index", help="Index a codebase and generate cache for all classes"
    )
    index_parser.add_argument("directory", help="Directory to index")
    add_common_args(index_parser)

    # Search command
    search_parser = subparsers.add_parser(
        "search", help="Search for a class using pre-built index"
    )
    search_parser.add_argument("class_name", help="Class name to search for")
    search_parser.add_argument(
        "-o", "--output", default="/tmp", help="Output directory for results"
    )
    search_parser.add_argument(
        "-f",
        "--format",
        default="png",
        choices=["png", "svg", "pdf"],
        help="Image output format",
    )
    add_common_args(search_parser)

    # Watch command (only if watchdog is available)
    if HAS_WATCHER:
        watch_parser = subparsers.add_parser(
            "watch", help="Watch directory for changes and update cache"
        )
        watch_parser.add_argument(
            "action", choices=["start", "stop", "status"], help="Action to perform"
        )
        watch_parser.add_argument(
            "directory", nargs="?", help="Directory to monitor (required for 'start')"
        )
        add_common_args(watch_parser)

    args = parser.parse_args()

    # If no command is specified, default to analyze for backward compatibility
    if not args.command:
        # For backward compatibility, treat positional arguments as analyze command
        if len(sys.argv) > 1 and not sys.argv[1].startswith("-"):
            args.command = "analyze"
            args.directory = sys.argv[1]
            if len(sys.argv) > 2 and not sys.argv[2].startswith("-"):
                args.class_name = sys.argv[2]
            else:
                print("Error: Missing class_name argument")
                parser.print_help()
                return 1
        else:
            parser.print_help()
            return 1

    # Dispatch to appropriate command handler
    if args.command == "analyze":
        return analyze_command(args)
    elif args.command == "index":
        return index_command(args)
    elif args.command == "search":
        return search_command(args)
    elif args.command == "watch" and HAS_WATCHER:
        return watcher_main()
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
