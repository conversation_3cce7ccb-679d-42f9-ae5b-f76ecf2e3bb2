/* Global styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    height: 100vh;
    overflow: hidden;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100%;
    margin: 0;
}

/* Sidebar styles */
.sidebar {
    background-color: #f0f0f0;
    padding: 20px;
    height: 100vh;
    overflow-y: auto;
    border-right: 1px solid #ddd;
}

.sidebar-header {
    padding-bottom: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.sidebar-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

/* Main content area styles */
.main-content {
    padding: 20px;
    height: 100vh;
    overflow-y: auto;
}

/* Card styles */
.card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 12px 15px;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Graph container styles */
#graphContainer {
    width: 100%;
    height: 500px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

/* Class selector styles */
#classSelect {
    width: 100%;
    height: 200px;
    overflow-y: auto;
}

/* Loading overlay styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Progress bar styles */
.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
}

.progress {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(45deg, #007bff, #0056b3);
    transition: width 0.3s ease;
    font-size: 0.75rem;
    line-height: 20px;
}

.progress-details {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Progress bar animation */
.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* Class details styles */
.class-detail-card {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.class-detail-card h4 {
    margin-top: 0;
    font-size: 1.1rem;
    color: #333;
}

.class-detail-card p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.class-detail-card .label {
    font-weight: bold;
    color: #555;
}

/* Node styles */
.node-class {
    background-color: #b8daff;
    border: 2px solid #4a86e8;
}

.node-target {
    background-color: #f8d7da;
    border: 2px solid #dc3545;
}

.node-struct {
    background-color: #d4edda;
    border: 2px solid #28a745;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar, .main-content {
        height: auto;
        overflow-y: visible;
    }

    .container-fluid, .row {
        height: auto;
    }

    #graphContainer {
        height: 300px;
    }
}
