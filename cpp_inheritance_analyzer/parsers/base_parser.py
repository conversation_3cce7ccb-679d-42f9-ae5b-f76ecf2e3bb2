#!/usr/bin/env python3
"""
Base parser interface for C++ code analysis.
"""

import hashlib
import json
import logging
import os
import sqlite3
import time
from abc import ABC, abstractmethod
from functools import lru_cache
from pathlib import Path

try:
    import tqdm

    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

from cpp_inheritance_analyzer.models.class_info import ClassInfo


class BaseParser(ABC):
    """Abstract base class for C++ parsers."""

    def __init__(self, use_cache: bool = True, cache_dir: str | None = None):
        """
        Initialize the parser.

        Args:
            use_cache: Whether to use persistent caching
            cache_dir: Directory to store cache files (default: ~/.cache/cpp_inheritance_analyzer)
        """
        self.logger = logging.getLogger(__name__)
        # In-memory cache for parsed files to avoid reprocessing in the same session
        self.file_cache: dict[str, list[ClassInfo]] = {}

        # Progress callback for real-time updates
        self.progress_callback = None

        # Persistent cache settings
        self.use_cache = use_cache
        if cache_dir:
            self.cache_dir = Path(cache_dir)
        else:
            self.cache_dir = Path.home() / ".cache" / "cpp_inheritance_analyzer"

        # Create cache directory if it doesn't exist
        if self.use_cache:
            os.makedirs(self.cache_dir, exist_ok=True)

            # Initialize SQLite cache database
            self.db_path = self.cache_dir / "parse_cache.db"
            self._init_cache_db()

    def _init_cache_db(self) -> None:
        """Initialize the SQLite cache database."""
        if not self.use_cache:
            return

        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Create tables if they don't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_cache (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT,
                    parser_name TEXT,
                    timestamp REAL,
                    content BLOB
                )
            """)

            # Create index for faster lookups
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_file_hash
                ON file_cache (file_hash)
            """)

            conn.commit()
            conn.close()
            self.logger.debug(f"Initialized SQLite cache at {self.db_path}")
        except Exception as e:
            self.logger.warning(f"Failed to initialize SQLite cache: {e}")
            # Fall back to file-based cache if SQLite fails
            self.use_cache = False

    def _get_file_hash(self, file_path: str) -> str:
        """
        Calculate a hash for a file based on its content and metadata.

        Args:
            file_path: Path to the file

        Returns:
            A hash string that uniquely identifies the file content
        """
        try:
            stat = os.stat(file_path)
            # Use file size and modification time for quick comparison
            file_meta = f"{file_path}:{stat.st_size}:{stat.st_mtime}"

            # For small files, include content hash for better accuracy
            if stat.st_size < 1024 * 1024:  # 1MB
                with open(file_path, "rb") as f:
                    content = f.read()
                content_hash = hashlib.md5(content).hexdigest()
                return hashlib.md5(f"{file_meta}:{content_hash}".encode()).hexdigest()
            else:
                # For large files, just use metadata to avoid reading the whole file
                return hashlib.md5(file_meta.encode()).hexdigest()
        except Exception as e:
            self.logger.warning(f"Failed to calculate hash for {file_path}: {e}")
            return ""

    def _get_cache_path(self, file_path: str) -> Path:
        """
        Get the cache file path for a source file.

        Args:
            file_path: Path to the source file

        Returns:
            Path to the cache file
        """
        file_hash = self._get_file_hash(file_path)
        if not file_hash:
            # If we can't calculate a hash, use a fallback based on the file path
            file_hash = hashlib.md5(file_path.encode()).hexdigest()

        # Use the parser class name as part of the cache file name to avoid conflicts
        parser_name = self.__class__.__name__
        cache_file = (
            f"{os.path.basename(file_path)}.{file_hash}.{parser_name}.cache.json"
        )
        return self.cache_dir / cache_file

    @lru_cache(maxsize=128)
    def _load_from_cache(self, file_path: str) -> tuple[bool, list[ClassInfo]]:
        """
        Try to load parsed classes from cache.

        Args:
            file_path: Path to the source file

        Returns:
            A tuple (success, classes) where success is True if cache was loaded successfully
        """
        if not self.use_cache:
            return False, []

        file_hash = self._get_file_hash(file_path)
        if not file_hash:
            return False, []

        try:
            # Try to load from SQLite cache first (faster)
            if hasattr(self, "db_path") and self.db_path.exists():
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()

                # Query the database
                cursor.execute(
                    "SELECT content FROM file_cache WHERE file_path = ? AND file_hash = ?",
                    (file_path, file_hash),
                )
                result = cursor.fetchone()
                conn.close()

                if result:
                    # Deserialize the cached data
                    cache_data = json.loads(result[0])

                    # Convert JSON data back to ClassInfo objects
                    classes = []
                    for class_data in cache_data.get("classes", []):
                        classes.append(
                            ClassInfo(
                                name=class_data["name"],
                                file_path=class_data["file_path"],
                                line_number=class_data["line_number"],
                                base_classes=class_data["base_classes"],
                                access_specifiers=class_data["access_specifiers"],
                                is_struct=class_data["is_struct"],
                                namespace=class_data["namespace"],
                                template_params=class_data["template_params"],
                            )
                        )

                    self.logger.debug(
                        f"Loaded {len(classes)} classes from SQLite cache for {file_path}"
                    )
                    return True, classes

            # Fall back to file-based cache if SQLite failed or not available
            cache_path = self._get_cache_path(file_path)
            if not cache_path.exists():
                return False, []

            with open(cache_path) as f:
                cache_data = json.load(f)

            # Verify file hasn't changed since cache was created
            if cache_data.get("file_hash") != file_hash:
                return False, []

            # Convert JSON data back to ClassInfo objects
            classes = []
            for class_data in cache_data.get("classes", []):
                classes.append(
                    ClassInfo(
                        name=class_data["name"],
                        file_path=class_data["file_path"],
                        line_number=class_data["line_number"],
                        base_classes=class_data["base_classes"],
                        access_specifiers=class_data["access_specifiers"],
                        is_struct=class_data["is_struct"],
                        namespace=class_data["namespace"],
                        template_params=class_data["template_params"],
                    )
                )

            self.logger.debug(
                f"Loaded {len(classes)} classes from file cache for {file_path}"
            )
            return True, classes
        except Exception as e:
            self.logger.warning(f"Failed to load cache for {file_path}: {e}")
            return False, []

    def _save_to_cache(self, file_path: str, classes: list[ClassInfo]) -> bool:
        """
        Save parsed classes to cache.

        Args:
            file_path: Path to the source file
            classes: List of ClassInfo objects to cache

        Returns:
            True if cache was saved successfully
        """
        if not self.use_cache:
            return False

        file_hash = self._get_file_hash(file_path)
        if not file_hash:
            return False

        # Convert ClassInfo objects to serializable dictionaries
        classes_data = []
        for class_info in classes:
            classes_data.append(
                {
                    "name": class_info.name,
                    "file_path": class_info.file_path,
                    "line_number": class_info.line_number,
                    "base_classes": class_info.base_classes,
                    "access_specifiers": class_info.access_specifiers,
                    "is_struct": class_info.is_struct,
                    "namespace": class_info.namespace,
                    "template_params": class_info.template_params,
                }
            )

        cache_data = {
            "file_path": file_path,
            "file_hash": file_hash,
            "timestamp": time.time(),
            "parser": self.__class__.__name__,
            "classes": classes_data,
        }

        try:
            # Try to save to SQLite cache first (faster for future lookups)
            if hasattr(self, "db_path"):
                try:
                    conn = sqlite3.connect(str(self.db_path))
                    cursor = conn.cursor()

                    # Serialize the data to JSON
                    serialized_data = json.dumps(cache_data)

                    # Insert or replace existing entry
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO file_cache
                        (file_path, file_hash, parser_name, timestamp, content)
                        VALUES (?, ?, ?, ?, ?)
                        """,
                        (
                            file_path,
                            file_hash,
                            self.__class__.__name__,
                            time.time(),
                            serialized_data,
                        ),
                    )

                    conn.commit()
                    conn.close()

                    self.logger.debug(
                        f"Saved {len(classes)} classes to SQLite cache for {file_path}"
                    )
                    return True
                except Exception as e:
                    self.logger.warning(
                        f"Failed to save to SQLite cache: {e}, falling back to file cache"
                    )

            # Fall back to file-based cache
            cache_path = self._get_cache_path(file_path)
            with open(cache_path, "w") as f:
                json.dump(cache_data, f, indent=2)

            self.logger.debug(
                f"Saved {len(classes)} classes to file cache for {file_path}"
            )
            return True
        except Exception as e:
            self.logger.warning(f"Failed to save cache for {file_path}: {e}")
            return False

    def set_progress_callback(self, callback):
        """Set a callback function for progress updates."""
        self.progress_callback = callback

    @abstractmethod
    def parse_file(self, file_path: str) -> list[ClassInfo]:
        """Parse a single C++ file and extract class information."""
        pass

    @abstractmethod
    def parse_directory(
        self, directory: str, extensions: set[str] = None, max_workers: int = None
    ) -> list[ClassInfo]:
        """Parse all C++ files in a directory tree."""
        pass
