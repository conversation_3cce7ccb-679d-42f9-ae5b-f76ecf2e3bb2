"""
Parser module for C++ inheritance analyzer.
"""

import logging

from cpp_inheritance_analyzer.parsers.base_parser import BaseParser
from cpp_inheritance_analyzer.parsers.regex_parser import CppParser

# Conditionally import Clang parser
try:
    from cpp_inheritance_analyzer.parsers.clang_parser import (
        CLANG_AVAILABLE,
        ClangCppParser,
    )
except ImportError:
    CLANG_AVAILABLE = False


def create_parser(
    parser_type: str = "auto", use_cache: bool = True, cache_dir: str | None = None
) -> BaseParser:
    """
    Factory function to create the appropriate parser.

    Args:
        parser_type: Type of parser to use ('regex', 'clang', or 'auto')
        use_cache: Whether to use persistent caching
        cache_dir: Directory to store cache files

    Returns:
        An instance of a BaseParser subclass
    """
    logger = logging.getLogger(__name__)

    if parser_type == "clang":
        if not CLANG_AVAILABLE:
            logger.warning(
                "Clang parser requested but not available. Falling back to regex parser."
            )
            return CppParser(use_cache=use_cache, cache_dir=cache_dir)
        else:
            logger.info("Using Clang parser for more accurate results")
            return ClangCppParser(use_cache=use_cache, cache_dir=cache_dir)
    elif parser_type == "auto":
        if CLANG_AVAILABLE:
            logger.info("Automatically selected Clang parser")
            return ClangCppParser(use_cache=use_cache, cache_dir=cache_dir)
        else:
            logger.info("Automatically selected regex parser (Clang not available)")
            return CppParser(use_cache=use_cache, cache_dir=cache_dir)
    else:  # Default to regex parser
        logger.info("Using regex parser")
        return CppParser(use_cache=use_cache, cache_dir=cache_dir)
