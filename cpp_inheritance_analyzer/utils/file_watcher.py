#!/usr/bin/env python3
"""
File system watcher for monitoring changes in C++ source files and updating cache.
"""

import argparse
import logging
import os
import signal
import sys
import threading
import time
from pathlib import Path

from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

from cpp_inheritance_analyzer.parsers import create_parser


class CppFileHandler(FileSystemEventHandler):
    """Handler for C++ file system events."""

    def __init__(self, parser, extensions=None):
        """
        Initialize the file handler.

        Args:
            parser: Parser instance to use for processing files
            extensions: Set of file extensions to monitor
        """
        self.logger = logging.getLogger(__name__)
        self.parser = parser
        self.extensions = extensions or {
            ".h",
            ".hpp",
            ".hxx",
            ".cpp",
            ".cc",
            ".cxx",
            ".c++",
        }
        self.logger.info(
            f"Monitoring files with extensions: {', '.join(self.extensions)}"
        )

    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory and self._is_cpp_file(event.src_path):
            self.logger.info(f"File modified: {event.src_path}")
            self._update_cache(event.src_path)

    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory and self._is_cpp_file(event.src_path):
            self.logger.info(f"File created: {event.src_path}")
            self._update_cache(event.src_path)

    def on_moved(self, event):
        """Handle file move events."""
        if not event.is_directory and self._is_cpp_file(event.dest_path):
            self.logger.info(f"File moved: {event.src_path} -> {event.dest_path}")
            # Remove old file from cache if it exists
            if (
                hasattr(self.parser, "file_cache")
                and event.src_path in self.parser.file_cache
            ):
                del self.parser.file_cache[event.src_path]
            self._update_cache(event.dest_path)

    def _is_cpp_file(self, path):
        """Check if a file is a C++ source file based on its extension."""
        return any(path.endswith(ext) for ext in self.extensions)

    def _update_cache(self, file_path):
        """Update the cache for a specific file."""
        try:
            # Force reparse the file
            if (
                hasattr(self.parser, "file_cache")
                and file_path in self.parser.file_cache
            ):
                del self.parser.file_cache[file_path]

            # Parse the file to update the cache
            classes = self.parser.parse_file(file_path)
            self.logger.info(
                f"Updated cache for {file_path} with {len(classes)} classes"
            )
        except Exception as e:
            self.logger.error(f"Failed to update cache for {file_path}: {e}")


class CacheWatcherDaemon:
    """Daemon for watching file system changes and updating cache."""

    def __init__(
        self,
        directory,
        extensions=None,
        parser_type="regex",
        cache_dir=None,
        log_level=logging.INFO,
    ):
        """
        Initialize the cache watcher daemon.

        Args:
            directory: Directory to monitor
            extensions: File extensions to monitor
            parser_type: Type of parser to use
            cache_dir: Directory to store cache files
            log_level: Logging level
        """
        self.directory = os.path.abspath(directory)
        self.extensions = extensions or {
            ".h",
            ".hpp",
            ".hxx",
            ".cpp",
            ".cc",
            ".cxx",
            ".c++",
        }
        self.parser_type = parser_type
        self.cache_dir = cache_dir

        # Configure logging
        logging.basicConfig(
            level=log_level,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(os.path.join("/tmp", "cpp_cache_watcher.log")),
            ],
        )
        self.logger = logging.getLogger(__name__)

        # Create parser
        self.parser = create_parser(parser_type, use_cache=True, cache_dir=cache_dir)

        # Initialize observer
        self.observer = Observer()
        self.running = False

        # Set up signal handlers only in the main thread
        if threading.current_thread() is threading.main_thread():
            try:
                signal.signal(signal.SIGINT, self._signal_handler)
                signal.signal(signal.SIGTERM, self._signal_handler)
                self.logger.debug("Signal handlers registered in main thread")
            except ValueError as e:
                self.logger.warning(f"Could not set signal handlers: {e}")
        else:
            self.logger.debug("Skipping signal handler registration in non-main thread")

    def _signal_handler(self, sig, frame):
        """Handle termination signals."""
        self.logger.info(f"Received signal {sig}, shutting down...")
        self.stop()

    def start(self):
        """Start the file system watcher."""
        if self.running:
            self.logger.warning("Watcher is already running")
            return

        # Validate directory
        if not os.path.isdir(self.directory):
            self.logger.error(f"Directory does not exist: {self.directory}")
            raise FileNotFoundError(f"Directory does not exist: {self.directory}")

        self.logger.info(
            f"Starting file system watcher for directory: {self.directory}"
        )
        self.logger.info(f"Using parser: {self.parser_type}")
        self.logger.info(
            f"Cache directory: {self.cache_dir or '~/.cache/cpp_inheritance_analyzer'}"
        )

        try:
            # Create event handler
            event_handler = CppFileHandler(self.parser, self.extensions)

            # Schedule directory monitoring
            self.logger.info(f"Scheduling directory monitoring: {self.directory}")
            self.observer.schedule(event_handler, self.directory, recursive=True)

            # Start the observer
            self.logger.info("Starting observer")
            self.observer.start()
            self.running = True

            self.logger.info("File system watcher started successfully")

            # Main loop
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt received, shutting down...")
                self.stop()

        except Exception as e:
            self.logger.error(f"Error starting file system watcher: {e}")
            import traceback

            self.logger.error(traceback.format_exc())
            raise

    def stop(self):
        """Stop the file system watcher."""
        if not self.running:
            return

        self.logger.info("Stopping file system watcher...")
        self.observer.stop()
        self.observer.join()
        self.running = False
        self.logger.info("File system watcher stopped")


def create_pid_file(pid_file):
    """Create a PID file for the daemon."""
    with open(pid_file, "w") as f:
        f.write(str(os.getpid()))


def remove_pid_file(pid_file):
    """Remove the PID file."""
    if os.path.exists(pid_file):
        os.remove(pid_file)


def is_daemon_running(pid_file):
    """Check if the daemon is running."""
    if not os.path.exists(pid_file):
        return False

    try:
        with open(pid_file, "r") as f:
            pid = int(f.read().strip())

        # Check if process exists
        os.kill(pid, 0)
        return True
    except (OSError, ValueError):
        # Process not running or PID file is invalid
        return False


def main():
    """Command-line entry point for the cache watcher daemon."""
    parser = argparse.ArgumentParser(description="C++ Cache Watcher Daemon")
    parser.add_argument(
        "action", choices=["start", "stop", "status"], help="Action to perform"
    )
    parser.add_argument(
        "directory", nargs="?", help="Directory to monitor (required for 'start')"
    )
    parser.add_argument(
        "--extensions",
        nargs="+",
        default=[".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx"],
        help="File extensions to monitor",
    )
    parser.add_argument(
        "--parser",
        default="regex",
        choices=["regex", "clang"],
        help="Parser to use (regex: faster, clang: more accurate)",
    )
    parser.add_argument(
        "--cache-dir",
        help="Directory to store cache files (default: ~/.cache/cpp_inheritance_analyzer)",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    # PID file location
    pid_file = "/tmp/cpp_cache_watcher.pid"

    if args.action == "status":
        if is_daemon_running(pid_file):
            print("Cache watcher daemon is running")
            return 0
        else:
            print("Cache watcher daemon is not running")
            return 1

    elif args.action == "stop":
        if not is_daemon_running(pid_file):
            print("Cache watcher daemon is not running")
            return 1

        try:
            with open(pid_file, "r") as f:
                pid = int(f.read().strip())

            print(f"Stopping cache watcher daemon (PID: {pid})...")
            os.kill(pid, signal.SIGTERM)

            # Wait for process to terminate
            max_wait = 5  # seconds
            for _ in range(max_wait):
                try:
                    os.kill(pid, 0)
                    time.sleep(1)
                except OSError:
                    break
            else:
                print("Warning: Daemon did not terminate gracefully, forcing...")
                try:
                    os.kill(pid, signal.SIGKILL)
                except OSError:
                    pass

            remove_pid_file(pid_file)
            print("Cache watcher daemon stopped")
            return 0
        except Exception as e:
            print(f"Error stopping daemon: {e}")
            return 1

    elif args.action == "start":
        if is_daemon_running(pid_file):
            print("Cache watcher daemon is already running")
            return 1

        if not args.directory:
            print("Error: Directory argument is required for 'start' action")
            return 1

        log_level = logging.DEBUG if args.verbose else logging.INFO

        print(f"Starting cache watcher daemon for directory: {args.directory}")
        print(f"Log file: /tmp/cpp_cache_watcher.log")

        # Create PID file
        create_pid_file(pid_file)

        try:
            watcher = CacheWatcherDaemon(
                args.directory,
                set(args.extensions),
                args.parser,
                args.cache_dir,
                log_level,
            )
            watcher.start()
            return 0
        except Exception as e:
            print(f"Error starting daemon: {e}")
            remove_pid_file(pid_file)
            return 1


if __name__ == "__main__":
    sys.exit(main())
