#!/usr/bin/env python3
"""
Main analyzer module for C++ inheritance analysis.
"""

import logging

from cpp_inheritance_analyzer.models.class_info import InheritanceGraph
from cpp_inheritance_analyzer.parsers import create_parser
from cpp_inheritance_analyzer.visualization.visualizer import GraphVisualizer


class InheritanceAnalyzer:
    """Analyze inheritance relationships and extract relevant subgraphs."""

    def __init__(self, graph: InheritanceGraph):
        self.graph = graph
        self.logger = logging.getLogger(__name__)

    def find_all_ancestors(self, class_name: str) -> set[str]:
        """Find all base classes (ancestors) of a given class."""
        ancestors = set()
        to_visit = [class_name]
        visited = set()

        while to_visit:
            current = to_visit.pop()
            if current in visited:
                continue
            visited.add(current)

            if current in self.graph.inheritance_map:
                parents = self.graph.inheritance_map[current]
                ancestors.update(parents)
                to_visit.extend(parents)

        return ancestors

    def find_all_descendants(self, class_name: str) -> set[str]:
        """Find all derived classes (descendants) of a given class."""
        descendants = set()
        to_visit = [class_name]
        visited = set()

        while to_visit:
            current = to_visit.pop()
            if current in visited:
                continue
            visited.add(current)

            if current in self.graph.reverse_inheritance_map:
                children = self.graph.reverse_inheritance_map[current]
                descendants.update(children)
                to_visit.extend(children)

        return descendants

    def find_siblings(self, class_name: str) -> set[str]:
        """Find sibling classes (classes with the same direct parents)."""
        siblings = set()

        if class_name not in self.graph.inheritance_map:
            return siblings

        parents = self.graph.inheritance_map[class_name]

        for parent in parents:
            if parent in self.graph.reverse_inheritance_map:
                # All children of this parent are siblings
                parent_children = self.graph.reverse_inheritance_map[parent]
                siblings.update(parent_children)

        # Remove the class itself
        siblings.discard(class_name)
        return siblings

    def extract_family_tree(self, target_class: str) -> InheritanceGraph:
        """Extract the family tree for a specific class."""
        # Find all relevant classes
        relevant_classes = {target_class}

        # Add all ancestors
        ancestors = self.find_all_ancestors(target_class)
        relevant_classes.update(ancestors)

        # Add all descendants
        descendants = self.find_all_descendants(target_class)
        relevant_classes.update(descendants)

        # Add siblings (but not their descendants)
        siblings = self.find_siblings(target_class)
        relevant_classes.update(siblings)

        # Create new graph with only relevant classes
        family_graph = InheritanceGraph()

        for class_name in relevant_classes:
            if class_name in self.graph.classes:
                family_graph.add_class(self.graph.classes[class_name])

        self.logger.info(
            f"Family tree for {target_class}: {len(relevant_classes)} related classes"
        )

        return family_graph


class CppInheritanceAnalyzer:
    """Main analyzer class that coordinates all components."""

    def __init__(
        self,
        log_level=logging.INFO,
        parser_type="regex",
        use_cache=True,
        cache_dir=None,
    ):
        """
        Initialize the analyzer with the specified parser type.

        Args:
            log_level: Logging level (default: INFO)
            parser_type: Type of parser to use ('regex', 'clang', or 'auto')
            use_cache: Whether to use persistent caching (default: True)
            cache_dir: Directory to store cache files (default: ~/.cache/cpp_inheritance_analyzer)
        """
        logging.basicConfig(
            level=log_level, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger(__name__)

        # Cache settings
        self.use_cache = use_cache
        self.cache_dir = cache_dir

        # Select parser based on type
        self.parser = create_parser(
            parser_type, use_cache=use_cache, cache_dir=cache_dir
        )

        self.visualizer = GraphVisualizer()
        self.graph = InheritanceGraph()
        self.analyzer = None

    def analyze_codebase(
        self, directory: str, extensions: set[str] = None
    ) -> InheritanceGraph:
        """Analyze an entire codebase and build inheritance graph."""
        self.logger.info(f"Analyzing codebase in: {directory}")

        # Parse all files
        try:
            from tqdm import tqdm

            has_tqdm = True
        except ImportError:
            has_tqdm = False

        # Check for interrupt flag
        import sys

        interrupted = False
        if "cpp_inheritance_analyzer" in sys.modules:
            # Get interrupt flag from main module
            interrupted = getattr(
                sys.modules["cpp_inheritance_analyzer"], "interrupted", False
            )

        # Parse all files with progress reporting
        all_classes = self.parser.parse_directory(directory, extensions)

        # If interrupted during file parsing, return partial results early
        if interrupted or getattr(
            sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
        ):
            self.logger.info(
                "Due to interruption, building inheritance graph with partially parsed data"
            )
            # Even in case of interruption, try to build a partial inheritance graph
            self.graph = InheritanceGraph()
            for class_info in all_classes:
                self.graph.add_class(class_info)
            self.analyzer = InheritanceAnalyzer(self.graph)
            self.logger.info(
                f"Built partial inheritance graph with {len(self.graph.classes)} classes"
            )
            return self.graph

        # Build inheritance graph with progress bar
        self.graph = InheritanceGraph()

        if has_tqdm and len(all_classes) > 100:
            # Use progress bar for large codebases
            try:
                with tqdm(
                    total=len(all_classes),
                    desc="Building inheritance graph",
                    unit="class",
                    ncols=100,
                    colour="blue",
                ) as pbar:
                    for i, class_info in enumerate(all_classes):
                        # Check interrupt flag periodically
                        if i % 100 == 0 and (
                            interrupted
                            or getattr(
                                sys.modules.get("cpp_inheritance_analyzer", None),
                                "interrupted",
                                False,
                            )
                        ):
                            self.logger.info(
                                "Interrupt signal detected, stopping full inheritance graph construction"
                            )
                            break
                        self.graph.add_class(class_info)
                        pbar.update(1)
            except KeyboardInterrupt:
                self.logger.info(
                    "Keyboard interrupt received, gracefully stopping inheritance graph construction..."
                )
                # Set interrupt flag
                if "cpp_inheritance_analyzer" in sys.modules:
                    sys.modules["cpp_inheritance_analyzer"].interrupted = True
        else:
            # No progress bar for small codebases
            try:
                for i, class_info in enumerate(all_classes):
                    # Check interrupt flag periodically
                    if i % 100 == 0 and (
                        interrupted
                        or getattr(
                            sys.modules.get("cpp_inheritance_analyzer", None),
                            "interrupted",
                            False,
                        )
                    ):
                        self.logger.info(
                            "Interrupt signal detected, stopping full inheritance graph construction"
                        )
                        break
                    self.graph.add_class(class_info)
            except KeyboardInterrupt:
                self.logger.info(
                    "Keyboard interrupt received, gracefully stopping inheritance graph construction..."
                )
                # Set interrupt flag
                if "cpp_inheritance_analyzer" in sys.modules:
                    sys.modules["cpp_inheritance_analyzer"].interrupted = True

        self.analyzer = InheritanceAnalyzer(self.graph)

        # Report processed data even in case of interruption
        if interrupted or getattr(
            sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
        ):
            self.logger.info(
                f"Due to interruption, built partial inheritance graph with {len(self.graph.classes)} classes"
            )
        else:
            self.logger.info(
                f"Built complete inheritance graph with {len(self.graph.classes)} classes"
            )

        return self.graph

    def _find_target_class(self, class_name: str) -> str | None:
        """
        Find a class by name in the inheritance graph.

        Args:
            class_name: Name of the class to find

        Returns:
            The fully qualified class name if found, None otherwise
        """
        # Find the class (try exact match first, then partial match)
        target_class = None
        for full_name in self.graph.classes.keys():
            if full_name == class_name or full_name.endswith(f"::{class_name}"):
                target_class = full_name
                break

        if not target_class:
            # Try partial match
            matches = [name for name in self.graph.classes.keys() if class_name in name]
            if len(matches) == 1:
                target_class = matches[0]
            elif len(matches) > 1:
                self.logger.warning(f"Multiple matches for '{class_name}': {matches}")
                self.logger.info(f"Using first match: {matches[0]}")
                target_class = matches[0]
            else:
                self.logger.error(f"Class '{class_name}' not found in codebase")
                return None

        return target_class

    def analyze_class(
        self, class_name: str, output_dir: str = "/tmp", image_format: str = "png"
    ) -> InheritanceGraph | None:
        """Analyze inheritance relationships for a specific class."""
        if not self.analyzer:
            raise ValueError("Must analyze codebase first")

        # Check for interrupt flag
        import sys

        interrupted = False
        if "cpp_inheritance_analyzer" in sys.modules:
            # Get interrupt flag from main module
            interrupted = getattr(
                sys.modules["cpp_inheritance_analyzer"], "interrupted", False
            )

        if interrupted:
            self.logger.warning(
                "Analysis was interrupted, cannot continue analyzing specific class"
            )
            return None

        try:
            # Find the target class
            target_class = self._find_target_class(class_name)
            if not target_class:
                return None

            # Check if interrupted during class lookup
            if interrupted or getattr(
                sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
            ):
                self.logger.warning(
                    "Analysis was interrupted, cannot continue extracting class inheritance tree"
                )
                return None

            # Extract family tree
            family_graph = self.analyzer.extract_family_tree(target_class)

            # Check if interrupted during inheritance tree extraction
            if interrupted or getattr(
                sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
            ):
                self.logger.warning(
                    "Analysis was interrupted, cannot generate visualization diagrams"
                )
                return family_graph

            # Generate visualizations
            self.visualizer.generate_visualizations(
                family_graph, target_class, output_dir, image_format
            )

            return family_graph

        except KeyboardInterrupt:
            self.logger.info(
                "Keyboard interrupt received, gracefully stopping specific class analysis..."
            )
            # Set interrupt flag
            if "cpp_inheritance_analyzer" in sys.modules:
                sys.modules["cpp_inheritance_analyzer"].interrupted = True
            return None

    def load_from_cache(self) -> bool:
        """
        Load all class information from cache without parsing files.

        Returns:
            True if cache was loaded successfully, False otherwise
        """
        self.logger.info("Loading class information from cache...")

        if not self.use_cache:
            self.logger.warning("Cache is disabled, cannot load from cache")
            return False

        if not hasattr(self.parser, "db_path") or not self.parser.db_path.exists():
            self.logger.warning("Cache database not found")
            return False

        try:
            import sqlite3
            import json
            from cpp_inheritance_analyzer.models.class_info import ClassInfo

            # Connect to the SQLite database
            conn = sqlite3.connect(str(self.parser.db_path))
            cursor = conn.cursor()

            # Query all cached files
            cursor.execute("SELECT content FROM file_cache")
            results = cursor.fetchall()
            conn.close()

            if not results:
                self.logger.warning("No cached data found")
                return False

            # Reset the graph
            self.graph = InheritanceGraph()
            all_classes = []

            # Process all cached files
            for result in results:
                try:
                    cache_data = json.loads(result[0])

                    # Convert JSON data back to ClassInfo objects
                    for class_data in cache_data.get("classes", []):
                        class_info = ClassInfo(
                            name=class_data["name"],
                            file_path=class_data["file_path"],
                            line_number=class_data["line_number"],
                            base_classes=class_data["base_classes"],
                            access_specifiers=class_data["access_specifiers"],
                            is_struct=class_data["is_struct"],
                            namespace=class_data["namespace"],
                            template_params=class_data["template_params"],
                        )
                        all_classes.append(class_info)
                except Exception as e:
                    self.logger.warning(f"Failed to process cached data: {e}")
                    continue

            # Build inheritance graph
            for class_info in all_classes:
                self.graph.add_class(class_info)

            self.analyzer = InheritanceAnalyzer(self.graph)
            self.logger.info(f"Loaded {len(self.graph.classes)} classes from cache")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load from cache: {e}")
            return False

    def search_class(
        self, class_name: str, output_dir: str = "/tmp", image_format: str = "png"
    ) -> InheritanceGraph | None:
        """
        Search for a class using the pre-built index and generate inheritance diagram.

        Args:
            class_name: Name of the class to search for
            output_dir: Directory to save output files
            image_format: Format for output images (png, svg, pdf)

        Returns:
            InheritanceGraph containing the class and its related classes, or None if not found
        """
        # Load from cache if graph is empty
        if not self.graph.classes:
            if not self.load_from_cache():
                self.logger.error("Failed to load class information from cache")
                self.logger.info("Try running 'index' command first to build the cache")
                return None

        # Now that we have loaded the graph, use analyze_class to find and visualize
        return self.analyze_class(class_name, output_dir, image_format)
