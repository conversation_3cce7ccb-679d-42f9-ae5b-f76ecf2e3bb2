#!/usr/bin/env python3
"""
Models for representing C++ class information and inheritance relationships.
"""

from dataclasses import dataclass, field


@dataclass
class ClassInfo:
    """Store information about a C++ class."""

    name: str
    file_path: str
    line_number: int
    base_classes: list[str] = field(default_factory=list)
    access_specifiers: list[str] = field(
        default_factory=list
    )  # public, private, protected
    is_struct: bool = False
    namespace: str = ""
    template_params: list[str] = field(default_factory=list)

    def full_name(self) -> str:
        """Get fully qualified name including namespace."""
        if self.namespace:
            return f"{self.namespace}::{self.name}"
        return self.name


@dataclass
class InheritanceGraph:
    """Store the complete inheritance relationship graph."""

    classes: dict[str, ClassInfo] = field(default_factory=dict)
    inheritance_map: dict[str, set[str]] = field(
        default_factory=dict
    )  # child -> parents
    reverse_inheritance_map: dict[str, set[str]] = field(
        default_factory=dict
    )  # parent -> children

    def add_class(self, class_info: ClassInfo) -> None:
        """Add a class to the graph."""
        full_name = class_info.full_name()
        self.classes[full_name] = class_info

        if full_name not in self.inheritance_map:
            self.inheritance_map[full_name] = set()
        if full_name not in self.reverse_inheritance_map:
            self.reverse_inheritance_map[full_name] = set()

        # Add inheritance relationships
        for base_class in class_info.base_classes:
            self.inheritance_map[full_name].add(base_class)
            if base_class not in self.reverse_inheritance_map:
                self.reverse_inheritance_map[base_class] = set()
            self.reverse_inheritance_map[base_class].add(full_name)
