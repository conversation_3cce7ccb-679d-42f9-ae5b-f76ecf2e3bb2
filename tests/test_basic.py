#!/usr/bin/env python3
"""
Basic tests for the C++ Inheritance Analyzer.
"""

import unittest

from cpp_inheritance_analyzer.models.class_info import ClassInfo, InheritanceGraph


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality of the analyzer."""

    def test_class_info(self):
        """Test ClassInfo class."""
        # Create a class info object
        class_info = ClassInfo(
            name="TestClass",
            file_path="test.hpp",
            line_number=10,
            base_classes=["BaseClass"],
            access_specifiers=["public"],
            is_struct=False,
            namespace="test",
            template_params=["T"],
        )

        # Check properties
        self.assertEqual(class_info.name, "TestClass")
        self.assertEqual(class_info.file_path, "test.hpp")
        self.assertEqual(class_info.line_number, 10)
        self.assertEqual(class_info.base_classes, ["BaseClass"])
        self.assertEqual(class_info.access_specifiers, ["public"])
        self.assertFalse(class_info.is_struct)
        self.assertEqual(class_info.namespace, "test")
        self.assertEqual(class_info.template_params, ["T"])

        # Check full_name method
        self.assertEqual(class_info.full_name(), "test::TestClass")

    def test_inheritance_graph(self):
        """Test InheritanceGraph class."""
        # Create a graph
        graph = InheritanceGraph()

        # Create class info objects
        base_class = ClassInfo(
            name="BaseClass", file_path="base.hpp", line_number=5, namespace="test"
        )

        derived_class = ClassInfo(
            name="DerivedClass",
            file_path="derived.hpp",
            line_number=10,
            base_classes=["test::BaseClass"],
            access_specifiers=["public"],
            namespace="test",
        )

        # Add classes to graph
        graph.add_class(base_class)
        graph.add_class(derived_class)

        # Check graph properties
        self.assertEqual(len(graph.classes), 2)
        self.assertIn("test::BaseClass", graph.classes)
        self.assertIn("test::DerivedClass", graph.classes)

        # Check inheritance relationships
        self.assertEqual(len(graph.inheritance_map["test::DerivedClass"]), 1)
        self.assertIn("test::BaseClass", graph.inheritance_map["test::DerivedClass"])

        # Check reverse relationships
        self.assertEqual(len(graph.reverse_inheritance_map["test::BaseClass"]), 1)
        self.assertIn(
            "test::DerivedClass", graph.reverse_inheritance_map["test::BaseClass"]
        )


if __name__ == "__main__":
    unittest.main()
