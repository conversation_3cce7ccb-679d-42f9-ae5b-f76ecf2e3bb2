#!/usr/bin/env python3
"""
Tests for the index and search functionality.
"""

import os
import tempfile
import unittest
from pathlib import Path

from cpp_inheritance_analyzer.analyzer import CppInheritanceAnalyzer


class TestIndexSearch(unittest.TestCase):
    """Test the index and search functionality."""

    def setUp(self):
        """Set up the test environment."""
        self.test_file = os.path.join(os.path.dirname(__file__), "test_classes.hpp")
        self.test_dir = os.path.dirname(__file__)

        # Create a temporary directory for cache
        self.temp_dir = tempfile.TemporaryDirectory()
        self.cache_dir = self.temp_dir.name

        # Create an analyzer with the test cache directory
        self.analyzer = CppInheritanceAnalyzer(
            parser_type="regex", use_cache=True, cache_dir=self.cache_dir
        )

    def tearDown(self):
        """Clean up after tests."""
        self.temp_dir.cleanup()

    def test_index_and_search(self):
        """Test indexing a directory and searching for a class."""
        # Index the test directory
        graph = self.analyzer.analyze_codebase(self.test_dir, {".hpp"})
        self.assertIsNotNone(graph)
        self.assertGreater(len(graph.classes), 0)

        # Create a new analyzer to ensure we're loading from cache
        search_analyzer = CppInheritanceAnalyzer(
            parser_type="regex", use_cache=True, cache_dir=self.cache_dir
        )

        # Search for a class
        family_graph = search_analyzer.search_class("Dog")
        self.assertIsNotNone(family_graph)
        self.assertGreater(len(family_graph.classes), 0)

        # Verify the class was found
        found = False
        for class_name in family_graph.classes:
            if "Dog" in class_name:
                found = True
                break
        self.assertTrue(found, "Dog class not found in search results")

    def test_load_from_cache(self):
        """Test loading class information from cache."""
        # First, index the test directory
        self.analyzer.analyze_codebase(self.test_dir, {".hpp"})

        # Create a new analyzer
        new_analyzer = CppInheritanceAnalyzer(
            parser_type="regex", use_cache=True, cache_dir=self.cache_dir
        )

        # Load from cache
        result = new_analyzer.load_from_cache()
        self.assertTrue(result)
        self.assertGreater(len(new_analyzer.graph.classes), 0)

    def test_search_without_index(self):
        """Test searching without indexing first."""
        # Create a new analyzer with an empty cache directory
        empty_temp_dir = tempfile.TemporaryDirectory()
        empty_analyzer = CppInheritanceAnalyzer(
            parser_type="regex", use_cache=True, cache_dir=empty_temp_dir.name
        )

        # Search should fail because no index exists
        family_graph = empty_analyzer.search_class("Dog")
        self.assertIsNone(family_graph)

        empty_temp_dir.cleanup()


if __name__ == "__main__":
    unittest.main()
