#!/usr/bin/env python3
"""
Tests for the C++ parsers.
"""

import os
import unittest

from cpp_inheritance_analyzer.parsers.regex_parser import CppParser

try:
    from cpp_inheritance_analyzer.parsers.clang_parser import (
        CLANG_AVAILABLE,
        ClangCppParser,
    )
except ImportError:
    CLANG_AVAILABLE = False


class TestParsers(unittest.TestCase):
    """Test the C++ parsers."""

    def setUp(self):
        """Set up the test environment."""
        self.test_file = os.path.join(os.path.dirname(__file__), "test_classes.hpp")
        self.regex_parser = CppParser()
        if CLANG_AVAILABLE:
            self.clang_parser = ClangCppParser()

    def test_regex_parser(self):
        """Test the regex parser."""
        classes = self.regex_parser.parse_file(self.test_file)

        # Check that we found all classes
        self.assertGreaterEqual(len(classes), 6)

        # Check class names
        class_names = [c.full_name() for c in classes]
        self.assertIn("test::Animal", class_names)
        self.assertIn("test::Mammal", class_names)
        self.assertIn("test::Pet", class_names)
        self.assertIn("test::Dog", class_names)

        # Check inheritance
        dog_class = next(c for c in classes if c.full_name() == "test::Dog")
        self.assertEqual(len(dog_class.base_classes), 2)
        self.assertIn("test::Mammal", dog_class.base_classes)
        self.assertIn("test::Pet", dog_class.base_classes)

    @unittest.skipIf(not CLANG_AVAILABLE, "Clang not available")
    def test_clang_parser(self):
        """Test the Clang parser."""
        classes = self.clang_parser.parse_file(self.test_file)

        # Check that we found all classes
        self.assertGreaterEqual(len(classes), 6)

        # Check class names
        class_names = [c.full_name() for c in classes]
        self.assertIn("test::Animal", class_names)
        self.assertIn("test::Mammal", class_names)
        self.assertIn("test::Pet", class_names)
        self.assertIn("test::Dog", class_names)

        # Check inheritance
        dog_class = next(c for c in classes if c.full_name() == "test::Dog")
        self.assertEqual(len(dog_class.base_classes), 2)
        self.assertIn("test::Mammal", dog_class.base_classes)
        self.assertIn("test::Pet", dog_class.base_classes)


if __name__ == "__main__":
    unittest.main()
