#pragma once

namespace test {

// Base class
class Animal {
public:
    virtual ~Animal() = default;
    virtual void makeSound() const = 0;
};

// Derived class
class Mammal : public Animal {
public:
    virtual void giveBirth() const {}
};

// Multiple inheritance
class Pet {
public:
    virtual ~Pet() = default;
    virtual void play() const = 0;
};

// Derived class with multiple inheritance
class Dog : public Mammal, public Pet {
public:
    void makeSound() const override {}
    void play() const override {}
};

// Template class
template <typename T>
class Container {
public:
    void add(const T& item) {}
};

// Template specialization
class DogContainer : public Container<Dog> {
public:
    void bark() {}
};

// Nested namespace
namespace inner {

class Cat : public Mammal, public Pet {
public:
    void makeSound() const override {}
    void play() const override {}
};

} // namespace inner

} // namespace test
