# Makefile for cpp-inheritance-analyzer
#
# This Makefile provides common commands for development, testing, and building
# the C++ Inheritance Analyzer project.

# Python interpreter and package manager
PYTHON := python3
UV := uv
PIP := $(UV) pip

# Project directories
SRC_DIR := cpp_inheritance_analyzer
TEST_DIR := tests
DIST_DIR := dist
BUILD_DIR := build

# Files to clean
CLEAN_FILES := $(DIST_DIR) $(BUILD_DIR) .pytest_cache .coverage htmlcov *.egg-info
CLEAN_PATTERNS := __pycache__ *.pyc *.pyo *.pyd .*.swp

# Default target
.PHONY: help
help:
	@echo "C++ Inheritance Analyzer Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make install        Install project dependencies"
	@echo "  make dev-install    Install development dependencies"
	@echo "  make test           Run tests"
	@echo "  make test-file      Run a specific test file (use TEST_FILE=path/to/test.py)"
	@echo "  make test-cov       Run tests with coverage"
	@echo "  make lint           Run code style checks using ruff"
	@echo "  make format         Format code using ruff"
	@echo "  make clean          Clean build artifacts and temporary files"
	@echo "  make build          Build the project"
	@echo "  make dist           Create distribution packages"
	@echo "  make run            Run the analyzer (use ARGS='dir class_name [options]')"
	@echo "  make api            Run the web API server"
	@echo "  make help           Show this help message"
	@echo ""

# Install project dependencies
.PHONY: install
install:
	$(PIP) install -e .

# Install development dependencies
.PHONY: dev-install
dev-install:
	$(PIP) install -e ".[dev]"

# Run tests
.PHONY: test
test:
	pytest $(TEST_DIR)

# Run a specific test file
# Usage: make test-file TEST_FILE=tests/test_basic.py
.PHONY: test-file
test-file:
	pytest $(TEST_FILE)

# Run tests with coverage
.PHONY: test-cov
test-cov:
	pytest --cov=$(SRC_DIR) $(TEST_DIR) --cov-report=term --cov-report=html

# Run code style checks
.PHONY: lint
lint:
	ruff check $(SRC_DIR) $(TEST_DIR)

# Format code
.PHONY: format
format:
	ruff format $(SRC_DIR) $(TEST_DIR)

# Clean build artifacts and temporary files
.PHONY: clean
clean:
	rm -rf $(CLEAN_FILES)
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".*.swp" -delete
	find . -type f -name ".coverage.*" -delete

# Build the project
.PHONY: build
build: clean
	$(PYTHON) -m pip install build
	$(PYTHON) -m build

# Create distribution packages
.PHONY: dist
dist: build
	$(PYTHON) -m build --sdist --wheel

# Run the analyzer with default settings
# Usage: make run ARGS="directory class_name [options]"
.PHONY: run
run:
	$(PYTHON) -m $(SRC_DIR) analyze $(ARGS)

# Start the file watcher daemon
# Usage: make watch-start DIR=directory [ARGS="--parser regex --verbose"]
.PHONY: watch-start
watch-start:
	$(PYTHON) -m $(SRC_DIR) watch start $(DIR) $(ARGS)

# Stop the file watcher daemon
.PHONY: watch-stop
watch-stop:
	$(PYTHON) -m $(SRC_DIR) watch stop

# Check the status of the file watcher daemon
.PHONY: watch-status
watch-status:
	$(PYTHON) -m $(SRC_DIR) watch status

# Run the web API server
.PHONY: api
api:
	cd $(shell pwd) && $(PYTHON) -m $(SRC_DIR).api.server

# Default target
.DEFAULT_GOAL := help
