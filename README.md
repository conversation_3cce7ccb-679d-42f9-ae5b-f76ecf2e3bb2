# C++ Class Inheritance Analyzer

A high-performance, modular tool for analyzing class inheritance relationships in C++ codebases and generating visual diagrams.

## Features

- Support for two parsers:
  - **Regex Parser**: Fast, suitable for simple codebases
  - **Clang/LLVM Parser**: More accurate, capable of handling complex C++ code
- Automatic detection of class inheritance relationships
- Support for multiple inheritance
- Support for namespaces
- Support for template classes
- Generation of visual inheritance diagrams (using Graphviz or Matplotlib)
- Export of analysis results in JSON format
- Persistent caching of parsed files for faster reanalysis
- File system watcher daemon for automatic cache updates
- Index generation for entire codebases without specifying a target class
- Fast search using pre-built index without re-parsing the codebase
- Web API and UI for visualizing inheritance relationships
- Graceful interrupt handling (Ctrl+C):
  - Saving of processed data
  - Generation of partial results during interruption
  - Providing friendly user feedback

## Project Structure

```
cpp_inheritance_analyzer/
├── __init__.py          # Package initialization and command-line entry point
├── __main__.py          # Module execution entry point
├── analyzer.py          # Main analyzer class
├── api/                 # Web API and UI
│   ├── __init__.py
│   ├── server.py        # Flask API server
│   └── static/          # Static web files (HTML, CSS, JS)
├── models/              # Data models
│   ├── __init__.py
│   └── class_info.py    # Class information and inheritance graph data structures
├── parsers/             # Parser implementations
│   ├── __init__.py      # Parser factory
│   ├── base_parser.py   # Base parser class
│   ├── regex_parser.py  # Regex parser
│   └── clang_parser.py  # Clang/LLVM parser
├── visualization/       # Visualization tools
│   ├── __init__.py
│   └── visualizer.py    # Graph generator
└── utils/               # Utility functions
    ├── __init__.py
    └── file_watcher.py  # File system watcher for monitoring changes
```

## Installation

### Dependencies

- Python 3.10 or higher
- Graphviz (optional, for generating diagrams)
- Matplotlib (optional, as an alternative to Graphviz)
- Clang/LLVM (optional, for more accurate C++ code parsing)

### Installation with pip

```bash
# Install from PyPI
pip install cpp-inheritance-analyzer

# Or install from source code
git clone https://github.com/joelongs/cpp-inheritance-analyzer.git
cd cpp-inheritance-analyzer
pip install -e .

# Install development dependencies (pytest, ruff, etc.)
pip install -e ".[dev]"
```

## Usage

### As a Command-Line Tool

```bash
# Analyze a specific class
cpp-inheritance-analyzer analyze <directory> <class_name> [options]

# Index an entire codebase (generate cache for all classes)
cpp-inheritance-analyzer index <directory> [options]

# Search for a class using pre-built index
cpp-inheritance-analyzer search <class_name> [options]

# Watch a directory for changes and update cache
cpp-inheritance-analyzer watch start <directory> [options]

# Or use the Python module
python -m cpp_inheritance_analyzer analyze <directory> <class_name> [options]
```

### As a Python Library

```python
from cpp_inheritance_analyzer.analyzer import CppInheritanceAnalyzer

# Create an analyzer instance
analyzer = CppInheritanceAnalyzer(parser_type="clang")

# Analyze the codebase
analyzer.analyze_codebase("./my_project")

# Analyze a specific class
family_graph = analyzer.analyze_class("MyClass", output_dir="/tmp", image_format="svg")

# Use the results
print(f"Found {len(family_graph.classes)} related classes")

# Search for a class using pre-built index
analyzer = CppInheritanceAnalyzer(parser_type="auto")
family_graph = analyzer.search_class("MyClass", output_dir="/tmp", image_format="svg")
```

### Parameters

- `<directory>`: C++ code directory to analyze
- `<class_name>`: Target class name to analyze

### Options

- `-o, --output`: Output directory (default: /tmp)
- `-f, --format`: Image output format (png, svg, or pdf, default: png)
- `-v, --verbose`: Enable verbose logging
- `--extensions`: File extensions to parse (default: .h, .hpp, .hxx, .cpp, .cc, .cxx)
- `--parser`: Parser to use (regex: faster but less accurate, clang: slower but more accurate, auto: automatically select the best available parser, default: auto)

### Examples

```bash
# Analyze MyClass with default settings
cpp-inheritance-analyzer analyze ./my_project MyClass

# Use the Clang parser and output in SVG format
cpp-inheritance-analyzer analyze ./my_project MyClass --parser clang -f svg

# Use the regex parser (faster)
cpp-inheritance-analyzer analyze ./my_project MyClass --parser regex

# Index an entire codebase to build cache
cpp-inheritance-analyzer index ./my_project --parser regex

# Search for a class using pre-built index
cpp-inheritance-analyzer search MyClass -f svg

# Watch a directory for changes and update cache automatically
cpp-inheritance-analyzer watch start ./my_project

# Start the web API server
make api
# Or directly (run from the project root directory):
cd /path/to/project && python -m cpp_inheritance_analyzer.api.server
```

## Output

The tool generates the following files:

1. `<class_name>_inheritance.<format>`: Inheritance diagram
2. `<class_name>_inheritance.dot`: Graphviz DOT file (can be used for custom diagrams)
3. `<class_name>_inheritance.json`: JSON file containing all analysis results

## Interrupt Handling

Analyzing large codebases can take a long time. This tool supports graceful handling of Ctrl+C interrupts:

- Press **Ctrl+C** once: The tool will gracefully stop processing, save the analyzed data, and generate partial results if possible
- Press **Ctrl+C** twice: Force exit the program (data may be lost)

After an interruption, the processed data is saved to the cache and can be reused in subsequent runs, avoiding the need to reanalyze the same files.

## Development

The project uses a Makefile to simplify common development tasks.

### Common Make Commands

```bash
# Install project dependencies
make install

# Install development dependencies
make dev-install

# Run tests
make test

# Run tests with coverage
make test-cov

# Format code
make format

# Check code style and quality
make lint

# Clean build artifacts
make clean
```

### Running Tests

```bash
# Run all tests
pytest
# or
make test

# Run tests and generate coverage report
pytest --cov=cpp_inheritance_analyzer
# or
make test-cov
```

### Code Formatting and Linting

```bash
# Format code
make format
# or directly:
ruff format cpp_inheritance_analyzer tests

# Check code style and quality
make lint
# or directly:
ruff check cpp_inheritance_analyzer tests
```

The project uses [ruff](https://github.com/astral-sh/ruff) for code formatting and linting, which is a fast Python linter and formatter written in Rust. It replaces multiple tools (black, isort, flake8, etc.) with a single, faster tool.

## License

MIT
