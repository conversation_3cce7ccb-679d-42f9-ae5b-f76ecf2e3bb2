#!/usr/bin/env python3
"""
Debug script to test the find_all_descendants method.
"""

from cpp_inheritance_analyzer.models.class_info import ClassInfo, InheritanceGraph
from cpp_inheritance_analyzer.analyzer import InheritanceAnalyzer

def test_descendants():
    """Test the descendants finding logic."""
    # Create a simple inheritance hierarchy
    # Field (base class)
    # ├── Field_str (derived)
    # ├── Field_num (derived)
    # └── Field_blob (derived)
    
    graph = InheritanceGraph()
    
    # Create base class
    field_class = ClassInfo(
        name="Field",
        file_path="field.h",
        line_number=100,
        base_classes=[],
        namespace=""
    )
    
    # Create derived classes
    field_str = ClassInfo(
        name="Field_str",
        file_path="field_str.h", 
        line_number=50,
        base_classes=["Field"],
        namespace=""
    )
    
    field_num = ClassInfo(
        name="Field_num",
        file_path="field_num.h",
        line_number=50,
        base_classes=["Field"],
        namespace=""
    )
    
    field_blob = ClassInfo(
        name="Field_blob",
        file_path="field_blob.h",
        line_number=50,
        base_classes=["Field"],
        namespace=""
    )
    
    # Add classes to graph
    graph.add_class(field_class)
    graph.add_class(field_str)
    graph.add_class(field_num)
    graph.add_class(field_blob)
    
    # Create analyzer
    analyzer = InheritanceAnalyzer(graph)
    
    # Debug: Print the reverse inheritance map
    print("Reverse inheritance map:")
    for parent, children in graph.reverse_inheritance_map.items():
        print(f"  {parent} -> {children}")
    
    # Test finding descendants of Field
    descendants = analyzer.find_all_descendants("Field")
    print(f"\nDescendants of Field: {descendants}")
    print(f"Number of descendants: {len(descendants)}")
    
    # Test the family tree extraction
    family_graph = analyzer.extract_family_tree("Field")
    print(f"\nFamily tree classes: {list(family_graph.classes.keys())}")
    print(f"Family tree size: {len(family_graph.classes)}")

if __name__ == "__main__":
    test_descendants()
