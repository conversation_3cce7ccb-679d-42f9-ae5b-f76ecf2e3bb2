# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Python-generated files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.env/
.venv/
.python-version

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Project specific
*.dot
*.png
*.svg
*.pdf
*.json
!pyproject.toml
!package.json

# Original cpp_inheritance_analyzer.py file
/cpp_inheritance_analyzer.py
