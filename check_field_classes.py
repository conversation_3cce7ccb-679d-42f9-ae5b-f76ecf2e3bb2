#!/usr/bin/env python3
"""
Check what Field-related classes are in the cache.
"""

import sqlite3
import json
from pathlib import Path

def check_field_classes():
    """Check Field-related classes in the cache."""
    # Find the cache database
    cache_dir = Path.home() / ".cache" / "cpp_inheritance_analyzer"
    db_path = cache_dir / "file_cache.db"
    
    if not db_path.exists():
        print(f"Cache database not found at {db_path}")
        return
    
    print(f"Checking cache database: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # Get all cached data
    cursor.execute("SELECT content FROM file_cache")
    results = cursor.fetchall()
    conn.close()
    
    print(f"Found {len(results)} cached files")
    
    # Look for Field-related classes
    field_classes = []
    all_classes = []
    
    for result in results:
        try:
            cache_data = json.loads(result[0])
            for class_data in cache_data.get("classes", []):
                class_name = class_data["name"]
                namespace = class_data.get("namespace", "")
                full_name = f"{namespace}::{class_name}" if namespace else class_name
                
                all_classes.append(full_name)
                
                # Look for Field-related classes
                if "Field" in class_name or "field" in class_name.lower():
                    field_classes.append({
                        "name": class_name,
                        "full_name": full_name,
                        "namespace": namespace,
                        "file_path": class_data["file_path"],
                        "base_classes": class_data["base_classes"],
                        "is_struct": class_data["is_struct"]
                    })
        except Exception as e:
            print(f"Error processing cache data: {e}")
            continue
    
    print(f"\nTotal classes found: {len(all_classes)}")
    print(f"Field-related classes found: {len(field_classes)}")
    
    if field_classes:
        print("\nField-related classes:")
        for cls in sorted(field_classes, key=lambda x: x["full_name"]):
            print(f"  {cls['full_name']}")
            print(f"    File: {cls['file_path']}")
            print(f"    Base classes: {cls['base_classes']}")
            print(f"    Is struct: {cls['is_struct']}")
            print()
    
    # Look specifically for "Field" base class
    field_base_classes = [cls for cls in field_classes if cls["name"] == "Field"]
    if field_base_classes:
        print(f"Found {len(field_base_classes)} 'Field' base classes:")
        for cls in field_base_classes:
            print(f"  {cls['full_name']} in {cls['file_path']}")
    
    # Look for classes that inherit from Field
    field_derived_classes = []
    for cls in field_classes:
        for base in cls["base_classes"]:
            if "Field" in base:
                field_derived_classes.append(cls)
                break
    
    print(f"\nClasses that inherit from Field: {len(field_derived_classes)}")
    for cls in field_derived_classes:
        print(f"  {cls['full_name']} -> {cls['base_classes']}")

if __name__ == "__main__":
    check_field_classes()
